document.addEventListener('DOMContentLoaded', function() {
    const tipoBusca = document.getElementById('tipoBusca');
    const campoBusca = document.getElementById('campoBusca');
    const campoData = document.getElementById('campoData');
    const labelBusca = document.getElementById('labelBusca');
    const valorBusca = document.getElementById('valorBusca');
    const formBusca = document.getElementById('formBusca');
    const loading = document.getElementById('loading');
    const resultados = document.getElementById('resultados');

    // Restaurar última busca do localStorage
    restaurarUltimaBusca();

    // Controlar campos de busca baseado no tipo selecionado
    tipoBusca.addEventListener('change', function() {
        const tipo = this.value;
        
        // Resetar campos
        campoBusca.style.display = 'none';
        campoData.style.display = 'none';
        valorBusca.value = '';
        document.getElementById('dataInicio').value = '';
        document.getElementById('dataFim').value = '';
        
        if (tipo === 'data') {
            campoData.style.display = 'block';
        } else if (tipo === 'whatsapp') {
            campoBusca.style.display = 'block';
            labelBusca.textContent = 'Número WhatsApp:';
            valorBusca.placeholder = 'Ex: 5535991125771 ou (35) 99112-5771';
        } else if (tipo === 'nome') {
            campoBusca.style.display = 'block';
            labelBusca.textContent = 'Nome do Cliente:';
            valorBusca.placeholder = 'Digite o nome do cliente';
        } else if (tipo === 'protocolo') {
            campoBusca.style.display = 'block';
            labelBusca.textContent = 'Protocolo:';
            valorBusca.placeholder = 'Ex: OPA20205 ou apenas 20205';
        }
    });

    // Submissão do formulário
    formBusca.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const dados = Object.fromEntries(formData);
        
        // Validações
        if (!dados.tipo_busca) {
            alert('Selecione um tipo de busca');
            return;
        }
        
        if (dados.tipo_busca === 'data' && !dados.data_inicio) {
            alert('Informe pelo menos a data de início');
            return;
        }
        
        if ((dados.tipo_busca === 'whatsapp' || dados.tipo_busca === 'nome' || dados.tipo_busca === 'protocolo') && !dados.valor_busca) {
            alert('Informe o valor para busca');
            return;
        }

        // Não salvar ainda - vamos salvar após obter os resultados
        
        // Mostrar loading
        loading.style.display = 'block';
        resultados.innerHTML = '';
        
        try {
            const response = await fetch('/api/buscar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dados)
            });
            
            const atendimentos = await response.json();
            
            if (response.ok) {
                exibirResultados(atendimentos, dados);
                // Salvar busca e resultados no localStorage
                salvarUltimaBusca(dados, atendimentos);
            } else {
                throw new Error(atendimentos.error || 'Erro na busca');
            }
            
        } catch (error) {
            console.error('Erro:', error);
            resultados.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erro ao realizar busca: ${error.message}
                </div>
            `;
        } finally {
            loading.style.display = 'none';
        }
    });

    function exibirResultados(atendimentos, dadosBusca = null) {
        if (atendimentos.length === 0) {
            resultados.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h5>Nenhum resultado encontrado</h5>
                    <p>Tente ajustar os critérios de busca.</p>
                </div>
            `;
            return;
        }

        // Informações da busca para exibir
        let infoBusca = '';
        if (dadosBusca) {
            const tipoBuscaTexto = {
                'data': 'Data',
                'whatsapp': 'WhatsApp',
                'nome': 'Nome',
                'protocolo': 'Protocolo'
            };

            infoBusca = `<small class="text-muted d-block mb-2">
                <i class="fas fa-info-circle me-1"></i>
                Busca por ${tipoBuscaTexto[dadosBusca.tipo_busca] || dadosBusca.tipo_busca}`;

            if (dadosBusca.valor_busca) {
                infoBusca += `: "${dadosBusca.valor_busca}"`;
            }
            if (dadosBusca.data_inicio) {
                infoBusca += `: ${dadosBusca.data_inicio}`;
                if (dadosBusca.data_fim) {
                    infoBusca += ` até ${dadosBusca.data_fim}`;
                }
            }
            infoBusca += '</small>';
        }

        let html = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h5><i class="fas fa-list me-2"></i>Resultados da Busca</h5>
                    ${infoBusca}
                </div>
                <span class="badge bg-success">${atendimentos.length} encontrado(s)</span>
            </div>
        `;

        atendimentos.forEach(atendimento => {
            const clienteUser = atendimento.cliente_info?.[0];
            const clientePrincipal = atendimento.cliente_principal_info?.[0];
            const atendente = atendimento.atendente_info?.[0];
            const dataFormatada = new Date(atendimento.date).toLocaleString('pt-BR');

            // Priorizar informações do cliente principal
            const clienteNome = clientePrincipal?.nome || clienteUser?.nome || 'Cliente não identificado';
            const clienteId = clientePrincipal?.id ? ` (${clientePrincipal.id})` : '';
            const nomeCompleto = clienteNome + clienteId;

            const iniciais = clienteNome ? clienteNome.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() : '??';

            // Status do atendimento
            let statusClass = 'status-F';
            let statusTexto = 'Finalizado';
            if (atendimento.status === 'A') {
                statusClass = 'status-A';
                statusTexto = 'Aberto';
            } else if (atendimento.status === 'E') {
                statusClass = 'status-E';
                statusTexto = 'Em Espera';
            }

            html += `
                <div class="resultado-item" onclick="abrirChat('${atendimento._id}')">
                    <div class="cliente-info">
                        <div class="cliente-avatar">${iniciais}</div>
                        <div class="cliente-dados flex-grow-1">
                            <h6>${nomeCompleto}</h6>
                            <small class="text-muted">
                                <i class="fab fa-whatsapp me-1"></i>${atendimento.canal_cliente || 'N/A'}
                            </small>
                        </div>
                        <div class="text-end">
                            <span class="status-badge ${statusClass}">${statusTexto}</span>
                        </div>
                    </div>
                    
                    <div class="atendimento-info">
                        <div class="row">
                            <div class="col-md-6">
                                <small><strong>Data:</strong> <span class="copyable-text" onclick="copiarTexto('${dataFormatada}'); event.stopPropagation();" title="Clique para copiar">${dataFormatada}</span></small><br>
                                <small><strong>Protocolo:</strong> <span class="copyable-text" onclick="copiarTexto('${atendimento.protocolo || 'N/A'}'); event.stopPropagation();" title="Clique para copiar">${atendimento.protocolo || 'N/A'}</span></small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>Atendente:</strong> <span class="copyable-text" onclick="copiarTexto('${atendente?.nome || 'N/A'}'); event.stopPropagation();" title="Clique para copiar">${atendente?.nome || 'N/A'}</span></small><br>
                                <small><strong>Canal:</strong> <span class="copyable-text" onclick="copiarTexto('${atendimento.canal || 'N/A'}'); event.stopPropagation();" title="Clique para copiar">${atendimento.canal || 'N/A'}</span></small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-2">
                        <button class="btn btn-success btn-sm me-2">
                            <i class="fas fa-comments me-1"></i>Ver Conversa
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportarPDFConversa('${atendimento._id}'); event.stopPropagation();">
                            <i class="fas fa-file-pdf me-1"></i>PDF
                        </button>
                    </div>
                </div>
            `;
        });

        resultados.innerHTML = html;
    }

    // Função para abrir chat
    window.abrirChat = function(idAtendimento) {
        window.location.href = `/chat/${idAtendimento}`;
    };

    // Função para exportar PDF de uma conversa
    window.exportarPDFConversa = function(idAtendimento) {
        const url = `/print/chat/${idAtendimento}`;
        window.location.href = url;
    };

    // Função para copiar texto
    window.copiarTexto = function(texto) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(texto).then(() => {
                mostrarToast('Texto copiado!', 'success');
            }).catch(() => {
                copiarTextoFallback(texto);
            });
        } else {
            copiarTextoFallback(texto);
        }
    };

    function copiarTextoFallback(texto) {
        const textArea = document.createElement('textarea');
        textArea.value = texto;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            mostrarToast('Texto copiado!', 'success');
        } catch (err) {
            mostrarToast('Erro ao copiar texto', 'error');
        }

        document.body.removeChild(textArea);
    }

    function mostrarToast(mensagem, tipo = 'info') {
        // Criar elemento do toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${tipo}`;
        toast.textContent = mensagem;

        // Adicionar ao body
        document.body.appendChild(toast);

        // Mostrar com animação
        setTimeout(() => toast.classList.add('show'), 100);

        // Remover após 3 segundos
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }

    // Máscara para número de WhatsApp
    valorBusca.addEventListener('input', function() {
        if (tipoBusca.value === 'whatsapp') {
            // Remove tudo que não é número
            let valor = this.value.replace(/\D/g, '');

            // Aplica máscara se necessário (opcional)
            if (valor.length >= 11) {
                valor = valor.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
            } else if (valor.length >= 7) {
                valor = valor.replace(/(\d{2})(\d{4})(\d+)/, '($1) $2-$3');
            } else if (valor.length >= 3) {
                valor = valor.replace(/(\d{2})(\d+)/, '($1) $2');
            }

            this.value = valor;
        }
    });

    // Funções para localStorage
    function salvarUltimaBusca(dados, resultados) {
        const dadosParaSalvar = {
            busca: dados,
            resultados: resultados,
            timestamp: Date.now()
        };
        localStorage.setItem('ultimaBusca', JSON.stringify(dadosParaSalvar));
    }

    function restaurarUltimaBusca() {
        const ultimaBusca = localStorage.getItem('ultimaBusca');
        if (ultimaBusca) {
            try {
                const dadosSalvos = JSON.parse(ultimaBusca);
                const dados = dadosSalvos.busca;

                // Verificar se os dados não são muito antigos (1 hora)
                const agora = Date.now();
                const umaHora = 60 * 60 * 1000;
                if (agora - dadosSalvos.timestamp > umaHora) {
                    localStorage.removeItem('ultimaBusca');
                    return;
                }

                // Restaurar tipo de busca primeiro
                if (dados.tipo_busca) {
                    tipoBusca.value = dados.tipo_busca;
                    // Disparar evento change manualmente
                    const evento = new Event('change');
                    tipoBusca.dispatchEvent(evento);

                    // Aguardar um pouco para os campos aparecerem
                    setTimeout(() => {
                        const event = new Event('change');
                        tipoBusca.dispatchEvent(event);

                        // Restaurar valores após os campos aparecerem
                        if (dados.valor_busca) {
                            valorBusca.value = dados.valor_busca;
                        }
                        if (dados.data_inicio) {
                            document.getElementById('dataInicio').value = dados.data_inicio;
                        }
                        if (dados.data_fim) {
                            document.getElementById('dataFim').value = dados.data_fim;
                        }

                        // Restaurar resultados se existirem
                        if (dadosSalvos.resultados && dadosSalvos.resultados.length > 0) {
                            exibirResultados(dadosSalvos.resultados, dados);
                        }
                    }, 100);
                }

            } catch (error) {
                console.error('Erro ao restaurar última busca:', error);
                localStorage.removeItem('ultimaBusca');
            }
        }
    }
});
