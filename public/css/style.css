body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background-color: #25d366;
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

.btn-success {
    background-color: #25d366;
    border-color: #25d366;
}

.btn-success:hover {
    background-color: #1ea952;
    border-color: #1ea952;
}

.bg-success {
    background-color: #25d366 !important;
}

.text-success {
    color: #25d366 !important;
}

.spinner-border.text-success {
    color: #25d366 !important;
}

.resultado-item {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.resultado-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.cliente-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.cliente-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #25d366;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 15px;
}

.cliente-dados h6 {
    margin: 0;
    color: #333;
}

.cliente-dados small {
    color: #666;
}

.atendimento-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
}

.status-F {
    background-color: #d4edda;
    color: #155724;
}

.status-A {
    background-color: #fff3cd;
    color: #856404;
}

.status-E {
    background-color: #f8d7da;
    color: #721c24;
}

.no-results {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-results i {
    font-size: 3em;
    margin-bottom: 20px;
    color: #ccc;
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .resultado-item {
        padding: 10px;
    }
    
    .cliente-avatar {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
}

/* Textos copiáveis */
.copyable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.copyable-text:hover {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

/* Toast notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-success {
    background-color: #28a745;
}

.toast-error {
    background-color: #dc3545;
}

.toast-info {
    background-color: #17a2b8;
}

@media (max-width: 768px) {
    .toast-notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }

    .toast-notification.show {
        transform: translateY(0);
    }
}
