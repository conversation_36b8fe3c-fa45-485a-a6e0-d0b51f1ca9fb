html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.h-100 {
    height: 100vh !important;
}

.chat-header {
    background-color: #25d366 !important;
    border-bottom: 1px solid #1ea952;
    min-height: 70px;
}

.chat-messages {
    background-color: #e5ddd5;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E");
    overflow-y: auto;
    padding: 20px !important;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.message.client {
    flex-direction: row;
}

.message.agent {
    flex-direction: row-reverse;
}

.message-avatar {
    flex-shrink: 0;
}

.avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ddd;
}

.message-content {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.message.client .message-content {
    background-color: #ffffff;
    border-bottom-left-radius: 5px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message.agent .message-content {
    background-color: #dcf8c6;
    border-bottom-right-radius: 5px;
}

/* Estilos para menus interativos */
.menu-options {
    margin-top: 8px;
    padding: 8px 0;
}

.menu-option {
    padding: 4px 8px;
    margin: 2px 0;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
    border-left: 3px solid #25d366;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.message-header strong {
    font-size: 0.9em;
    color: #1976d2;
}

.message.agent .message-header strong {
    color: #25d366;
}

.message-time {
    font-size: 0.75em;
    color: #666;
    margin-left: 10px;
}

.message-text {
    font-size: 0.95em;
    line-height: 1.4;
    color: #333;
}

.date-separator {
    text-align: center;
    margin: 20px 0;
}

.date-badge {
    background-color: rgba(0,0,0,0.1);
    color: #666;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8em;
    display: inline-block;
}

.btn-outline-light {
    border-color: rgba(255,255,255,0.5);
}

.btn-outline-light:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.7);
}

/* Scrollbar personalizada */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.3);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.5);
}

/* Responsivo */
@media (max-width: 768px) {
    .message-content {
        max-width: 85%;
    }
    
    .chat-header {
        min-height: 60px;
    }
    
    .chat-header h5 {
        font-size: 1.1em;
    }
    
    .chat-messages {
        padding: 15px !important;
    }
}

/* Animações */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para arquivos/mídia */
.message-media {
    margin-top: 8px;
    border-radius: 8px;
    overflow: hidden;
    max-width: 300px;
}

.message-image {
    width: 100%;
    height: auto;
    display: block;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.message-image:hover {
    opacity: 0.9;
}

.message-file {
    background-color: rgba(0,0,0,0.05);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.3s ease;
}

.message-file:hover {
    background-color: rgba(0,0,0,0.1);
    color: inherit;
    text-decoration: none;
}

.file-icon {
    font-size: 1.5em;
    margin-right: 10px;
    color: #666;
}

.file-info {
    flex-grow: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.file-size {
    font-size: 0.8em;
    color: #666;
}

.download-btn {
    background: none;
    border: none;
    color: #25d366;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.download-btn:hover {
    background-color: rgba(37, 211, 102, 0.1);
}

/* Modal para visualização de imagens */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
}

.image-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
}

.image-modal img {
    width: 100%;
    height: auto;
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

.image-modal-close:hover {
    color: #ccc;
}

/* Estilos para citações de mensagens */
.message-quote {
    background-color: rgba(0, 0, 0, 0.1);
    border-left: 3px solid #25d366;
    padding: 8px 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

.quote-content {
    color: #666;
    font-style: italic;
}

/* Estilos para mensagens de sistema */
.system-message {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    font-style: italic;
}

/* Player de áudio customizado */
.audio-player {
    background-color: #f8f9fa;
    border-radius: 20px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    max-width: 300px;
}

.audio-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.audio-play-btn {
    background: #25d366;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.audio-play-btn:hover {
    background: #128c7e;
}

.audio-progress {
    flex: 1;
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.audio-progress-bar {
    height: 100%;
    background: #25d366;
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s;
}

.audio-time {
    font-size: 0.8em;
    color: #666;
    min-width: 35px;
    text-align: right;
}
