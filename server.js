const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const { MongoClient, ObjectId } = require('mongodb');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 8500;

// Configuração do MongoDB
const MONGO_URL = 'mongodb://localhost:27017';
const DB_NAME = 'suite';

let db;

// Middleware
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Conectar ao MongoDB
async function connectToMongo() {
    try {
        const client = new MongoClient(MONGO_URL);
        await client.connect();
        db = client.db(DB_NAME);
        console.log('Conectado ao MongoDB com sucesso!');
    } catch (error) {
        console.error('Erro ao conectar ao MongoDB:', error);
        process.exit(1);
    }
}

// Rota principal - página de busca
app.get('/', (req, res) => {
    res.render('index');
});

// API para buscar atendimentos
app.post('/api/buscar', async (req, res) => {
    try {
        const { tipo_busca, valor_busca, data_inicio, data_fim } = req.body;

        let query = {};
        let useAggregation = false;

        // Construir query baseada no tipo de busca
        if (tipo_busca === 'data' && data_inicio) {
            const inicio = new Date(data_inicio);
            const fim = data_fim ? new Date(data_fim) : new Date(data_inicio);
            fim.setHours(23, 59, 59, 999); // Final do dia

            query.date = {
                $gte: inicio,
                $lte: fim
            };
        } else if (tipo_busca === 'whatsapp' && valor_busca) {
            // Buscar por número de WhatsApp (pode estar em canal_cliente)
            query.canal_cliente = new RegExp(valor_busca.replace(/\D/g, ''), 'i');
        } else if (tipo_busca === 'protocolo' && valor_busca) {
            // Buscar por protocolo (adicionar OPA se não tiver)
            let protocoloBusca = valor_busca.trim();
            if (!protocoloBusca.toUpperCase().startsWith('OPA')) {
                protocoloBusca = 'OPA' + protocoloBusca;
            }
            query.protocolo = new RegExp(protocoloBusca, 'i');
        } else if (tipo_busca === 'nome' && valor_busca) {
            useAggregation = true;
        }

        let atendimentos;

        if (useAggregation || tipo_busca === 'nome') {
            // Usar agregação para busca por nome (com lookup correto)
            const pipeline = [
                {
                    $lookup: {
                        from: 'clientes_users',
                        localField: 'id_user',
                        foreignField: '_id',
                        as: 'cliente_info'
                    }
                },
                {
                    $lookup: {
                        from: 'usuarios',
                        localField: 'id_atendente',
                        foreignField: '_id',
                        as: 'atendente_info'
                    }
                },
                // Adicionar lookup para clientes principais
                {
                    $lookup: {
                        from: 'clientes',
                        localField: 'cliente_info.cli_emp',
                        foreignField: '_id',
                        as: 'cliente_principal_info'
                    }
                }
            ];

            // Adicionar filtros específicos
            if (tipo_busca === 'nome' && valor_busca) {
                pipeline.push({
                    $match: {
                        $or: [
                            { 'cliente_info.nome': new RegExp(valor_busca, 'i') },
                            { 'cliente_principal_info.nome': new RegExp(valor_busca, 'i') }
                        ]
                    }
                });
            } else {
                pipeline.push({ $match: query });
            }

            pipeline.push(
                { $sort: { date: -1 } },
                { $limit: 50 }
            );

            atendimentos = await db.collection('atendimentos').aggregate(pipeline).toArray();
        } else {
            // Buscar atendimentos com informações do cliente (método tradicional)
            atendimentos = await db.collection('atendimentos').aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: 'clientes_users',
                        localField: 'id_user',
                        foreignField: '_id',
                        as: 'cliente_info'
                    }
                },
                {
                    $lookup: {
                        from: 'usuarios',
                        localField: 'id_atendente',
                        foreignField: '_id',
                        as: 'atendente_info'
                    }
                },
                // Adicionar lookup para clientes principais
                {
                    $lookup: {
                        from: 'clientes',
                        localField: 'cliente_info.cli_emp',
                        foreignField: '_id',
                        as: 'cliente_principal_info'
                    }
                },
                { $sort: { date: -1 } },
                { $limit: 50 }
            ]).toArray();
        }

        res.json(atendimentos);

    } catch (error) {
        console.error('Erro na busca:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// API para buscar mensagens de um atendimento
app.get('/api/mensagens/:id_atendimento', async (req, res) => {
    try {
        const { id_atendimento } = req.params;

        // Buscar mensagens do atendimento com arquivos
        const mensagens = await db.collection('atendimentos_mensagens').aggregate([
            {
                $match: {
                    id_rota: new ObjectId(id_atendimento)
                }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'usuario_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_atend',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            },
            {
                $lookup: {
                    from: 'arquivos',
                    localField: 'objeto',
                    foreignField: '_id',
                    as: 'arquivo_info'
                }
            },
            // Buscar mensagem citada se existir
            {
                $lookup: {
                    from: 'atendimentos_mensagens',
                    localField: 'id_mensagem_citada',
                    foreignField: '_id',
                    as: 'mensagem_citada_info'
                }
            },
            { $sort: { data: 1 } }
        ]).toArray();

        // Buscar informações do atendimento com cliente e atendente
        const atendimento = await db.collection('atendimentos').aggregate([
            {
                $match: { _id: new ObjectId(id_atendimento) }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'cliente_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_atendente',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            },
            // Adicionar lookup para clientes principais via cli_emp
            {
                $lookup: {
                    from: 'clientes',
                    localField: 'cliente_info.cli_emp',
                    foreignField: '_id',
                    as: 'cliente_principal_info'
                }
            }
        ]).toArray();

        res.json({
            atendimento: atendimento[0] || null,
            mensagens
        });

    } catch (error) {
        console.error('Erro ao buscar mensagens:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// Rota para visualizar chat
app.get('/chat/:id_atendimento', (req, res) => {
    res.render('chat', { id_atendimento: req.params.id_atendimento });
});

// Rota para servir arquivos
app.get('/arquivo/:id_arquivo', async (req, res) => {
    try {
        const { id_arquivo } = req.params;
        
        // Primeiro tenta buscar no banco de dados
        try {
            // Valida se é um ObjectId válido
            if (ObjectId.isValid(id_arquivo)) {
                const arquivo = await db.collection('arquivos').findOne({
                    _id: new ObjectId(id_arquivo)
                });

                if (arquivo) {
                    const filePath = path.join(__dirname, arquivo.local);
                    
                    if (fs.existsSync(filePath)) {
                        res.setHeader('Content-Type', arquivo.tipo);
                        res.setHeader('Content-Disposition', `inline; filename="${arquivo.nome}"`);
                        return res.sendFile(filePath);
                    }
                }
            }
        } catch (dbError) {
            console.log('Busca no banco falhou, tentando servir arquivo direto:', dbError.message);
            // Continua para tentar servir o arquivo diretamente
        }

        // Fallback: tenta servir o arquivo diretamente pelo ID
        const directFilePath = path.join(__dirname, 'arquivos', id_arquivo);
        const directFilePathWithExtension = path.join(__dirname, 'arquivos', `${id_arquivo}.jpg`);
        
        if (fs.existsSync(directFilePath)) {
            res.setHeader('Content-Type', 'image/jpeg');
            res.setHeader('Content-Disposition', `inline; filename="${id_arquivo}"`);
            return res.sendFile(directFilePath);
        }
        
        if (fs.existsSync(directFilePathWithExtension)) {
            res.setHeader('Content-Type', 'image/jpeg');
            res.setHeader('Content-Disposition', `inline; filename="${id_arquivo}.jpg"`);
            return res.sendFile(directFilePathWithExtension);
        }

        // Se nenhum arquivo foi encontrado
        return res.status(404).json({ error: 'Arquivo não encontrado' });

    } catch (error) {
        console.error('Erro ao servir arquivo:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// API para informações do arquivo
app.get('/api/arquivo/:id_arquivo', async (req, res) => {
    try {
        const { id_arquivo } = req.params;
        const arquivo = await db.collection('arquivos').findOne({
            _id: new ObjectId(id_arquivo)
        });

        if (!arquivo) {
            return res.status(404).json({ error: 'Arquivo não encontrado' });
        }

        res.json(arquivo);

    } catch (error) {
        console.error('Erro ao buscar arquivo:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// Rota para visualizar conversa em formato para impressão/PDF
app.get('/print/chat/:id_atendimento', async (req, res) => {
    try {
        const { id_atendimento } = req.params;

        // Buscar dados da conversa diretamente do banco
        const mensagens = await db.collection('atendimentos_mensagens').aggregate([
            {
                $match: {
                    id_rota: new ObjectId(id_atendimento)
                }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'usuario_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_atend',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            },
            {
                $lookup: {
                    from: 'arquivos',
                    localField: 'objeto',
                    foreignField: '_id',
                    as: 'arquivo_info'
                }
            },
            // Buscar mensagem citada se existir
            {
                $lookup: {
                    from: 'atendimentos_mensagens',
                    localField: 'id_mensagem_citada',
                    foreignField: '_id',
                    as: 'mensagem_citada_info'
                }
            },
            { $sort: { data: 1 } }
        ]).toArray();

        const atendimento = await db.collection('atendimentos').aggregate([
            {
                $match: { _id: new ObjectId(id_atendimento) }
            },
            {
                $lookup: {
                    from: 'clientes_users',
                    localField: 'id_user',
                    foreignField: '_id',
                    as: 'cliente_info'
                }
            },
            {
                $lookup: {
                    from: 'usuarios',
                    localField: 'id_atendente',
                    foreignField: '_id',
                    as: 'atendente_info'
                }
            },
            // Adicionar lookup para clientes principais via cli_emp
            {
                $lookup: {
                    from: 'clientes',
                    localField: 'cliente_info.cli_emp',
                    foreignField: '_id',
                    as: 'cliente_principal_info'
                }
            }
        ]).toArray();

        const data = {
            atendimento: atendimento[0] || null,
            mensagens
        };

        if (!data.atendimento) {
            return res.status(404).send('Atendimento não encontrado');
        }

        // Renderizar página para impressão
        res.render('print-chat', { data });

    } catch (error) {
        console.error('Erro ao gerar página de impressão:', error);
        res.status(500).send('Erro interno do servidor');
    }
});

// Rota alternativa para PDF usando jsPDF no frontend
app.get('/pdf/chat/:id_atendimento', async (req, res) => {
    try {
        const { id_atendimento } = req.params;

        // Redirecionar para a página de impressão
        res.redirect(`/print/chat/${id_atendimento}`);

    } catch (error) {
        console.error('Erro ao gerar PDF:', error);
        res.status(500).json({ error: 'Erro ao gerar PDF' });
    }
});



// Iniciar servidor
connectToMongo().then(() => {
    app.listen(PORT, () => {
        console.log(`Servidor rodando na porta ${PORT}`);
        console.log(`Acesse: http://localhost:${PORT}`);
    });
});
